'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Locale } from '../../lib/i18n';
import { ProductWithDetails, Category, Subcategory } from '../../types/mysql-database';
import { useCart } from '../../lib/session-cart';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';

interface MobileProductDetailPageProps {
  locale: Locale;
  initialProduct?: ProductWithDetails | null;
  initialCategory?: Category | null;
  initialSubcategory?: Subcategory | null;
  productId: string;
}

const MobileProductDetailPage: React.FC<MobileProductDetailPageProps> = ({
  locale,
  initialProduct,
  initialCategory,
  initialSubcategory,
  productId
}) => {
  const router = useRouter();
  const { addToCart } = useCart();
  
  const [product, setProduct] = useState<ProductWithDetails | null>(initialProduct || null);
  const [category, setCategory] = useState<Category | null>(initialCategory || null);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(initialSubcategory || null);
  const [loading, setLoading] = useState(!initialProduct);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('description');
  const [showImageModal, setShowImageModal] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // النصوص
  const content = {
    ar: {
      loading: 'جاري التحميل...',
      notFound: 'المنتج غير موجود',
      notFoundMessage: 'المنتج المطلوب غير موجود أو تم حذفه',
      backToProducts: 'العودة للمنتجات',
      available: 'متوفر',
      unavailable: 'غير متوفر',
      code: 'الرمز',
      category: 'الفئة',
      subcategory: 'الفئة الفرعية',
      description: 'الوصف',
      specifications: 'المواصفات',
      features: 'المميزات',
      quantity: 'الكمية',
      addToCart: 'إضافة للسلة',
      whatsapp: 'واتساب',
      share: 'مشاركة',
      favorite: 'المفضلة',
      viewImages: 'عرض الصور',
      closeImages: 'إغلاق',
      addedToCart: 'تم إضافة المنتج للسلة بنجاح',
      errorAddingToCart: 'حدث خطأ في إضافة المنتج للسلة',
      productUnavailable: 'المنتج غير متوفر حالياً',
      relatedProducts: 'منتجات ذات صلة',
      viewAll: 'عرض الكل'
    },
    en: {
      loading: 'Loading...',
      notFound: 'Product Not Found',
      notFoundMessage: 'The requested product was not found or has been deleted',
      backToProducts: 'Back to Products',
      available: 'Available',
      unavailable: 'Unavailable',
      code: 'Code',
      category: 'Category',
      subcategory: 'Subcategory',
      description: 'Description',
      specifications: 'Specifications',
      features: 'Features',
      quantity: 'Quantity',
      addToCart: 'Add to Cart',
      whatsapp: 'WhatsApp',
      share: 'Share',
      favorite: 'Favorite',
      viewImages: 'View Images',
      closeImages: 'Close',
      addedToCart: 'Product added to cart successfully',
      errorAddingToCart: 'Error adding product to cart',
      productUnavailable: 'Product is currently unavailable',
      relatedProducts: 'Related Products',
      viewAll: 'View All'
    }
  };

  const t = content[locale];

  // جلب البيانات عند التحميل
  useEffect(() => {
    const fetchProductData = async () => {
      if (initialProduct) return;

      try {
        setLoading(true);

        // جلب تفاصيل المنتج
        const productResponse = await fetch(`/api/products/${productId}`);
        if (productResponse.ok) {
          const productResult = await productResponse.json();
          if (productResult.success && productResult.data) {
            setProduct(productResult.data);

            // جلب بيانات الفئة إذا كانت متوفرة
            if (productResult.data.category_id) {
              const categoryResponse = await fetch(`/api/categories?id=${productResult.data.category_id}`);
              if (categoryResponse.ok) {
                const categoryResult = await categoryResponse.json();
                if (categoryResult.success && categoryResult.data) {
                  setCategory(categoryResult.data);
                }
              }
            }

            // جلب بيانات الفئة الفرعية إذا كانت متوفرة
            if (productResult.data.subcategory_id) {
              const subcategoryResponse = await fetch(`/api/subcategories?id=${productResult.data.subcategory_id}`);
              if (subcategoryResponse.ok) {
                const subcategoryResult = await subcategoryResponse.json();
                if (subcategoryResult.success && subcategoryResult.data) {
                  setSubcategory(subcategoryResult.data);
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error fetching product data:', error);
        showToast(
          locale === 'ar' ? 'حدث خطأ في تحميل البيانات' : 'Error loading data',
          'error'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [productId, initialProduct, locale]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const handleAddToCart = () => {
    if (!product || !product.is_available) {
      showToast(t.productUnavailable, 'error');
      return;
    }

    try {
      addToCart({
        id: product.id,
        title: locale === 'ar' ? product.title_ar : product.title,
        image: product.images?.[0]?.image_url || '/placeholder-image.jpg',
        price: product.price || 0,
        quantity: quantity
      });

      showToast(t.addedToCart, 'success');
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast(t.errorAddingToCart, 'error');
    }
  };

  const handleWhatsApp = () => {
    if (!product) return;

    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const productUrl = `${window.location.origin}/${locale}/product/${product.id}`;
    const message = locale === 'ar'
      ? `مرحباً، أريد الاستفسار عن هذا المنتج:\n${productTitle}\n${productUrl}`
      : `Hello, I would like to inquire about this product:\n${productTitle}\n${productUrl}`;

    const whatsappUrl = `https://wa.me/+966599252259?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleShare = async () => {
    if (!product) return;

    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const productUrl = `${window.location.origin}/${locale}/product/${product.id}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: productTitle,
          text: locale === 'ar' ? product.description_ar : product.description,
          url: productUrl,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(productUrl);
        showToast(
          locale === 'ar' ? 'تم نسخ الرابط' : 'Link copied',
          'success'
        );
      } catch (error) {
        console.error('Error copying to clipboard:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={t.loading}
          showBackButton={true}
          backUrl={`/${locale}/products`}
        />
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">{t.loading}</p>
          </div>
        </div>
        <MobileBottomNav locale={locale} />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={t.notFound}
          showBackButton={true}
          backUrl={`/${locale}/products`}
        />
        <div className="flex items-center justify-center py-16">
          <div className="text-center px-4">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-error-warning-line text-gray-400 text-3xl"></i>
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">{t.notFound}</h2>
            <p className="text-gray-600 mb-8 text-sm">{t.notFoundMessage}</p>
            <Link
              href={`/${locale}/products`}
              className="bg-primary text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center gap-2"
            >
              <i className="ri-arrow-left-line"></i>
              {t.backToProducts}
            </Link>
          </div>
        </div>
        <MobileBottomNav locale={locale} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={locale === 'ar' ? product.title_ar : product.title}
        showBackButton={true}
        backUrl={`/${locale}/products`}
        customActions={
          <div className="flex items-center gap-2">
            <button
              onClick={handleShare}
              className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
            >
              <i className="ri-share-line text-gray-600"></i>
            </button>
            <button className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center">
              <i className="ri-heart-line text-gray-600"></i>
            </button>
          </div>
        }
      />

      {/* Product Content */}
      <div className="pb-24">
        {/* Product Images */}
        <div className="bg-white">
          <div className="relative">
            <div className="aspect-square bg-gray-50">
              {product.images && product.images.length > 0 ? (
                <Image
                  src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
                  alt={locale === 'ar' ? product.title_ar : product.title}
                  fill
                  className="object-cover"
                  priority
                  onClick={() => setShowImageModal(true)}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <i className="ri-image-line text-4xl mb-2"></i>
                    <p className="text-sm">{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Image Indicators */}
            {product.images && product.images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <div className="flex space-x-2 bg-black/20 rounded-full px-3 py-2">
                  {product.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === selectedImageIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Availability Badge */}
            <div className="absolute top-4 right-4">
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                product.is_available
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {product.is_available ? t.available : t.unavailable}
              </span>
            </div>
          </div>

          {/* Image Thumbnails */}
          {product.images && product.images.length > 1 && (
            <div className="px-4 py-3">
              <div className="flex space-x-2 overflow-x-auto">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors ${
                      index === selectedImageIndex ? 'border-primary' : 'border-gray-200'
                    }`}
                  >
                    <Image
                      src={image.image_url}
                      alt={`${locale === 'ar' ? product.title_ar : product.title} ${index + 1}`}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="bg-white mt-2 px-4 py-4">
          <div className="space-y-4">
            {/* Title and Price */}
            <div>
              <h1 className="text-xl font-bold text-gray-900 mb-2">
                {locale === 'ar' ? product.title_ar : product.title}
              </h1>
              {product.price && (
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-primary">
                    {product.price.toFixed(2)} {locale === 'ar' ? 'ريال' : 'SAR'}
                  </span>
                </div>
              )}
            </div>

            {/* Product Meta */}
            <div className="flex flex-wrap gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <i className="ri-barcode-line"></i>
                <span>{t.code}: {product.id.slice(-8)}</span>
              </div>
              {category && (
                <div className="flex items-center gap-1">
                  <i className="ri-folder-line"></i>
                  <span>{locale === 'ar' ? category.name_ar : category.name}</span>
                </div>
              )}
              {subcategory && (
                <div className="flex items-center gap-1">
                  <i className="ri-folder-2-line"></i>
                  <span>{locale === 'ar' ? subcategory.name_ar : subcategory.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white mt-2">
          <div className="flex border-b border-gray-200">
            {['description', 'specifications', 'features'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
                  activeTab === tab
                    ? 'text-primary border-b-2 border-primary'
                    : 'text-gray-600'
                }`}
              >
                {t[tab as keyof typeof t]}
              </button>
            ))}
          </div>

          <div className="px-4 py-4">
            {activeTab === 'description' && (
              <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {locale === 'ar' ? product.description_ar : product.description}
                </p>
              </div>
            )}
            {activeTab === 'specifications' && (
              <div className="space-y-3">
                <div className="text-gray-600 text-sm">
                  {locale === 'ar' 
                    ? 'المواصفات التفصيلية للمنتج ستكون متاحة قريباً'
                    : 'Detailed specifications will be available soon'
                  }
                </div>
              </div>
            )}
            {activeTab === 'features' && (
              <div className="space-y-3">
                <div className="text-gray-600 text-sm">
                  {locale === 'ar' 
                    ? 'مميزات المنتج ستكون متاحة قريباً'
                    : 'Product features will be available soon'
                  }
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Fixed Bottom Actions */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-pb">
        <div className="flex items-center gap-3">
          {/* Quantity Selector */}
          <div className="flex items-center bg-gray-100 rounded-lg">
            <button
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
              className="w-10 h-10 flex items-center justify-center text-gray-600"
            >
              <i className="ri-subtract-line"></i>
            </button>
            <span className="px-4 py-2 text-sm font-medium min-w-[3rem] text-center">
              {quantity}
            </span>
            <button
              onClick={() => setQuantity(quantity + 1)}
              className="w-10 h-10 flex items-center justify-center text-gray-600"
            >
              <i className="ri-add-line"></i>
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex-1 grid grid-cols-2 gap-3">
            <button
              onClick={handleWhatsApp}
              className="bg-green-500 text-white py-3 px-4 rounded-lg font-semibold flex items-center justify-center gap-2"
            >
              <i className="ri-whatsapp-line"></i>
              <span className="text-sm">{t.whatsapp}</span>
            </button>
            
            <button
              onClick={handleAddToCart}
              disabled={!product.is_available}
              className="bg-primary text-white py-3 px-4 rounded-lg font-semibold disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              <i className="ri-shopping-cart-line"></i>
              <span className="text-sm">{t.addToCart}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && product.images && product.images.length > 0 && (
        <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
          <button
            onClick={() => setShowImageModal(false)}
            className="absolute top-4 right-4 w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white z-10"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
          
          <div className="w-full h-full flex items-center justify-center p-4">
            <Image
              src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
              alt={locale === 'ar' ? product.title_ar : product.title}
              width={800}
              height={800}
              className="max-w-full max-h-full object-contain"
            />
          </div>

          {product.images.length > 1 && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex space-x-2 bg-black/50 rounded-full px-4 py-2">
                {product.images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === selectedImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileProductDetailPage;
