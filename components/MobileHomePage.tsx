// هذا الملف تم نقله إلى components/mobile/MobileHomePage.tsx
// يرجى استخدام المسار الجديد

export { default } from './mobile/MobileHomePage';
import { Locale } from '../lib/i18n';
import { Category, ProductWithDetails } from '../types/mysql-database';
import { useSiteSettings } from '../hooks/useSiteSettings';
import MobileToast from './MobileToast';

// نوع عنصر السلة
interface CartItem {
  id: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
}

interface MobileHomePageProps {
  locale: Locale;
  categories?: Category[];
  featuredProducts?: ProductWithDetails[];
}

const MobileHomePage: React.FC<MobileHomePageProps> = ({ 
  locale, 
  categories: initialCategories, 
  featuredProducts: initialProducts 
}) => {
  const { settings } = useSiteSettings();
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [featuredProducts, setFeaturedProducts] = useState<ProductWithDetails[]>(initialProducts || []);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [cartCount, setCartCount] = useState(0);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // Banner images from settings or default
  const bannerImages = settings?.heroImages?.filter(img => img.trim() !== '') || [
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop'
  ];

  // Auto-rotate banner
  useEffect(() => {
    if (bannerImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % bannerImages.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [bannerImages.length]);

  // Fetch data if not provided
  useEffect(() => {
    if (!initialCategories) {
      fetch('/api/categories')
        .then(res => res.json())
        .then(result => {
          if (result.success && Array.isArray(result.data)) {
            setCategories(result.data.filter((cat: Category) => cat.is_active));
          }
        })
        .catch(console.error);
    }

    if (!initialProducts) {
      fetch('/api/products?featured=true')
        .then(res => res.json())
        .then(result => {
          if (Array.isArray(result)) {
            setFeaturedProducts(result.slice(0, 6));
          } else if (result.success && Array.isArray(result.data)) {
            setFeaturedProducts(result.data.slice(0, 6));
          }
        })
        .catch(console.error);
    }

    // تحديث عداد السلة
    const updateCartCount = () => {
      try {
        const cart = localStorage.getItem('cart');
        if (cart) {
          const cartItems = JSON.parse(cart) as CartItem[];
          const totalItems = cartItems.reduce((sum: number, item) => sum + (item.quantity || 1), 0);
          setCartCount(totalItems);
        }
      } catch (error) {
        console.error('Error reading cart:', error);
      }
    };

    updateCartCount();

    // مراقبة تغييرات localStorage
    const handleStorageChange = () => updateCartCount();
    window.addEventListener('storage', handleStorageChange);

    // مراقبة تغييرات السلة عبر custom event
    window.addEventListener('cartUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cartUpdated', handleStorageChange);
    };
  }, [initialCategories, initialProducts]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({
      message,
      type,
      isVisible: true
    });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/${locale}/products?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  const addToCart = (product: ProductWithDetails) => {
    if (!product.is_available) return;

    try {
      const cartItem = {
        id: product.id,
        title: locale === 'ar' ? product.title_ar : product.title,
        image: product.images?.[0]?.image_url || '/placeholder-image.jpg',
        price: product.price || 0,
        quantity: 1
      };

      const existingCart = localStorage.getItem('cart');
      const cart = existingCart ? JSON.parse(existingCart) as CartItem[] : [];

      const existingItemIndex = cart.findIndex((item) => item.id === product.id);

      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += 1;
      } else {
        cart.push(cartItem);
      }

      localStorage.setItem('cart', JSON.stringify(cart));

      // إرسال event لتحديث عداد السلة
      window.dispatchEvent(new Event('cartUpdated'));

      // إظهار رسالة نجاح
      showToast(
        locale === 'ar'
          ? 'تم إضافة المنتج للسلة بنجاح'
          : 'Product added to cart successfully',
        'success'
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile App Header */}
      <div className="sticky top-0 bg-white shadow-sm z-50">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between mb-3">
            {/* Logo */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">DH</span>
              </div>
              <span className="font-bold text-gray-900">
                {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
              </span>
            </div>

            {/* Header Actions */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowSearch(!showSearch)}
                className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
              >
                <i className="ri-search-line text-gray-600"></i>
              </button>

              {/* WhatsApp Button */}
              <a
                href={`https://wa.me/${settings?.communicationSettings?.whatsapp?.businessNumber || '+966501234567'}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن منتجاتكم' : 'Hello, I would like to inquire about your products')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="w-9 h-9 bg-green-100 rounded-full flex items-center justify-center"
                title={locale === 'ar' ? 'تواصل عبر الواتساب' : 'Contact via WhatsApp'}
              >
                <i className="ri-whatsapp-line text-green-600"></i>
              </a>

              {/* Language Toggle */}
              <Link
                href={locale === 'ar' ? '/en' : '/ar'}
                className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
                title={locale === 'ar' ? 'English' : 'العربية'}
              >
                <span className="text-xs font-bold text-gray-600">
                  {locale === 'ar' ? 'EN' : 'ع'}
                </span>
              </Link>

              <Link
                href={`/${locale}/cart`}
                className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center relative"
              >
                <i className="ri-shopping-cart-line text-gray-600"></i>
                {cartCount > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {cartCount > 99 ? '99+' : cartCount}
                  </span>
                )}
              </Link>
            </div>
          </div>

          {/* Search Bar */}
          {showSearch && (
            <div className="flex items-center gap-2 animate-fadeInUp">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={locale === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                  className="w-full px-4 py-2 bg-gray-100 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <i className="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
              <button
                onClick={handleSearch}
                className="px-4 py-2 bg-primary text-white rounded-full text-sm font-medium"
              >
                {locale === 'ar' ? 'بحث' : 'Search'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Hero Banner */}
      <div className="relative h-48 overflow-hidden">
        {bannerImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentBannerIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <Image
              src={image}
              alt={`Banner ${index + 1}`}
              fill
              className="object-cover"
              priority={index === 0}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
          </div>
        ))}
        
        {/* Banner Content */}
        <div className="absolute bottom-4 left-4 right-4 text-white">
          <h1 className="text-xl font-bold mb-1">
            {locale === 'ar' ? 'تجهيزات البوفيه الفاخرة' : 'Premium Buffet Equipment'}
          </h1>
          <p className="text-sm opacity-90">
            {locale === 'ar' ? 'أفضل المعدات لفندقك ومطعمك' : 'Best equipment for your hotel & restaurant'}
          </p>
        </div>

        {/* Banner Indicators */}
        {bannerImages.length > 1 && (
          <div className="absolute bottom-2 right-4 flex gap-1">
            {bannerImages.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentBannerIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="px-4 py-4">
        <div className="grid grid-cols-4 gap-3">
          <Link
            href={`/${locale}/categories`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-grid-line text-blue-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'الفئات' : 'Categories'}
            </span>
          </Link>

          <Link
            href={`/${locale}/products`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-product-hunt-line text-green-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </span>
          </Link>

          <Link
            href={`/${locale}/contact`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-customer-service-line text-purple-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'اتصل بنا' : 'Contact'}
            </span>
          </Link>

          <Link
            href={`/${locale}/about`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-information-line text-orange-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'من نحن' : 'About'}
            </span>
          </Link>
        </div>
      </div>

      {/* Categories Section */}
      {categories.length > 0 && (
        <div className="px-4 pb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-bold text-gray-900">
              {locale === 'ar' ? 'تصفح الفئات' : 'Browse Categories'}
            </h2>
            <Link
              href={`/${locale}/categories`}
              className="text-primary text-sm font-medium"
            >
              {locale === 'ar' ? 'عرض الكل' : 'View All'}
            </Link>
          </div>

          <div className="grid grid-cols-2 gap-3">
            {categories.slice(0, 6).map((category) => (
              <Link
                key={category.id}
                href={`/${locale}/category/${category.id}`}
                className="bg-white rounded-xl overflow-hidden shadow-sm active:scale-95 transition-transform"
              >
                <div className="aspect-video relative">
                  {category.image_url ? (
                    <Image
                      src={category.image_url}
                      alt={locale === 'ar' ? category.name_ar : category.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <i className="ri-image-line text-gray-400 text-2xl"></i>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-2 left-2 right-2">
                    <h3 className="text-white font-semibold text-sm">
                      {locale === 'ar' ? category.name_ar : category.name}
                    </h3>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Featured Products */}
      {featuredProducts.length > 0 && (
        <div className="px-4 pb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-bold text-gray-900">
              {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
            </h2>
            <Link
              href={`/${locale}/products`}
              className="text-primary text-sm font-medium"
            >
              {locale === 'ar' ? 'عرض الكل' : 'View All'}
            </Link>
          </div>

          <div className="space-y-3">
            {featuredProducts.map((product) => (
              <div
                key={product.id}
                className="flex bg-white rounded-xl overflow-hidden shadow-sm"
              >
                <Link
                  href={`/${locale}/product/${product.id}`}
                  className="w-20 h-20 relative flex-shrink-0"
                >
                  {product.images?.[0]?.image_url ? (
                    <Image
                      src={product.images[0].image_url}
                      alt={locale === 'ar' ? product.title_ar : product.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <i className="ri-image-line text-gray-400"></i>
                    </div>
                  )}
                </Link>

                <div className="flex-1 p-3">
                  <Link href={`/${locale}/product/${product.id}`}>
                    <h3 className="font-semibold text-gray-900 text-sm mb-1 line-clamp-1">
                      {locale === 'ar' ? product.title_ar : product.title}
                    </h3>
                    <p className="text-gray-600 text-xs mb-2 line-clamp-2">
                      {locale === 'ar' ? product.description_ar : product.description}
                    </p>
                  </Link>

                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      product.is_available
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.is_available
                        ? (locale === 'ar' ? 'متوفر' : 'Available')
                        : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')
                      }
                    </span>

                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        addToCart(product);
                      }}
                      disabled={!product.is_available}
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                        product.is_available
                          ? 'bg-primary text-white hover:bg-primary/90 active:scale-95'
                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      <i className="ri-shopping-cart-line"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 safe-area-pb">
        <div className="flex items-center justify-around">
          <Link
            href={`/${locale}`}
            className="flex flex-col items-center py-2 px-3 text-primary"
          >
            <i className="ri-home-line text-xl mb-1"></i>
            <span className="text-xs font-medium">
              {locale === 'ar' ? 'الرئيسية' : 'Home'}
            </span>
          </Link>

          <Link
            href={`/${locale}/categories`}
            className="flex flex-col items-center py-2 px-3 text-gray-600"
          >
            <i className="ri-grid-line text-xl mb-1"></i>
            <span className="text-xs font-medium">
              {locale === 'ar' ? 'الفئات' : 'Categories'}
            </span>
          </Link>

          <Link
            href={`/${locale}/products`}
            className="flex flex-col items-center py-2 px-3 text-gray-600"
          >
            <i className="ri-product-hunt-line text-xl mb-1"></i>
            <span className="text-xs font-medium">
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </span>
          </Link>

          <Link
            href={`/${locale}/cart`}
            className="flex flex-col items-center py-2 px-3 text-gray-600 relative"
          >
            <i className="ri-shopping-cart-line text-xl mb-1"></i>
            <span className="text-xs font-medium">
              {locale === 'ar' ? 'السلة' : 'Cart'}
            </span>
            {cartCount > 0 && (
              <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {cartCount > 9 ? '9+' : cartCount}
              </span>
            )}
          </Link>

          <Link
            href={`/${locale}/contact`}
            className="flex flex-col items-center py-2 px-3 text-gray-600"
          >
            <i className="ri-customer-service-line text-xl mb-1"></i>
            <span className="text-xs font-medium">
              {locale === 'ar' ? 'اتصل بنا' : 'Contact'}
            </span>
          </Link>
        </div>
      </div>

      {/* Bottom Padding for Fixed Navigation */}
      <div className="h-20"></div>

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileHomePage;
